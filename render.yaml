# Render.com Configuration pour CONNECT

services:
  # Frontend Next.js
  - type: web
    name: connect-frontend
    runtime: node
    plan: starter
    repo: https://github.com/LG15601/connect.git
    buildCommand: cd frontend && npm ci && npm run build
    startCommand: cd frontend && npm start
    envVars:
      - key: NEXT_PUBLIC_SUPABASE_URL
        value: ""
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: ""
      - key: NEXT_PUBLIC_BACKEND_URL
        value: https://connect-backend.onrender.com/api
      - key: NEXT_PUBLIC_URL
        value: https://orchestraconnect.fr
      - key: NEXT_PUBLIC_ENV_MODE
        value: production
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_GOOGLE_CLIENT_ID
        value: ""
      - key: NEXT_PUBLIC_SENTRY_DSN
        value: ""
      - key: NEXT_PUBLIC_VERCEL_ENV
        value: production

  # Backend API
  - type: web
    name: connect-backend
    runtime: python
    plan: starter
    repo: https://github.com/LG15601/connect.git
    buildCommand: cd backend && pip install -r requirements.txt
    startCommand: cd backend && uvicorn api:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: ENV_MODE
        value: production
      - key: SUPABASE_URL
        value: ""
      - key: SUPABASE_ANON_KEY
        value: ""
      - key: SUPABASE_SERVICE_ROLE_KEY
        value: ""
      - key: REDIS_HOST
        value: master-robin-17412.upstash.io
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_PASSWORD
        value: ""
      - key: REDIS_SSL
        value: "true"
      - key: REDIS_URL
        value: ""
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672
      - key: ANTHROPIC_API_KEY
        value: ""
      - key: OPENAI_API_KEY
        value: ""
      - key: MODEL_TO_USE
        value: ""
      - key: AWS_ACCESS_KEY_ID
        value: ""
      - key: AWS_SECRET_ACCESS_KEY
        value: ""
      - key: AWS_REGION_NAME
        value: ""
      - key: GROQ_API_KEY
        value: ""
      - key: OPENROUTER_API_KEY
        value: ""
      - key: RAPID_API_KEY
        value: ""
      - key: TAVILY_API_KEY
        value: ""
      - key: FIRECRAWL_API_KEY
        value: ""
      - key: FIRECRAWL_URL
        value: https://api.firecrawl.dev
      - key: DAYTONA_API_KEY
        value: ""
      - key: DAYTONA_SERVER_URL
        value: https://app.daytona.io/api
      - key: DAYTONA_TARGET
        value: us
      - key: LANGFUSE_PUBLIC_KEY
        value: ""
      - key: LANGFUSE_SECRET_KEY
        value: ""
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com
      - key: SMITHERY_API_KEY
        value: ""

  # Worker
  - type: worker
    name: connect-worker
    runtime: python
    plan: standard
    repo: https://github.com/LG15601/connect.git
    buildCommand: cd backend && pip install -r requirements.txt
    startCommand: cd backend && python -m dramatiq run_agent_background --processes 1 --threads 2
    envVars:
      - key: DRAMATIQ_THREADS
        value: "2"
      - key: DRAMATIQ_PROCESSES
        value: "1"
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      - key: PYTHONUNBUFFERED
        value: "1"
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com
      - key: LANGFUSE_SECRET_KEY
        value: ""
      - key: LANGFUSE_PUBLIC_KEY
        value: ""
      - key: AWS_REGION_NAME
        value: ""
      - key: AWS_SECRET_ACCESS_KEY
        value: ""
      - key: AWS_ACCESS_KEY_ID
        value: ""
      - key: RAPID_API_KEY
        value: placeholder
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: REDIS_SSL
        value: "true"
      - key: REDIS_PASSWORD
        value: ""
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_HOST
        value: master-robin-17412.upstash.io
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672
      - key: REDIS_URL
        value: ""
      - key: DAYTONA_TARGET
        value: us

  # RabbitMQ
  - type: pserv
    name: connect-rabbitmq
    runtime: docker
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./rabbitmq.Dockerfile
    envVars:
      - key: RABBITMQ_URL
        value: amqp://guest:guest@localhost:5672
    openPorts:
      - port: 4369
        protocol: tcp
      - port: 5672
        protocol: tcp
      - port: 15692
        protocol: tcp
      - port: 25672
        protocol: tcp 