# Render.com Configuration pour CONNECT

services:
  # Frontend Next.js
  - type: web
    name: connect-frontend
    runtime: docker
    plan: starter
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./frontend/Dockerfile
    dockerContext: ./frontend
    envVars:
      - key: NEXT_PUBLIC_SUPABASE_URL
        value: https://irfvjdismpsxwihifsel.supabase.co
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MjIxNDAsImV4cCI6MjA2NTM5ODE0MH0.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI
      # Sentry Configuration
      - key: NEXT_PUBLIC_VERCEL_ENV
        value: production
      - key: SENTRY_ORG
        value: ""
      - key: SENTRY_PROJECT
        value: ""
      - key: SENTRY_AUTH_TOKEN
        value: ""
      - key: NEXT_PUBLIC_BACKEND_URL
        value: https://connect-backend.onrender.com/api
      - key: NEXT_PUBLIC_URL
        value: https://orchestraconnect.fr
      - key: NEXT_PUBLIC_ENV_MODE
        value: production
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_GOOGLE_CLIENT_ID
        value: ""
      - key: NEXT_PUBLIC_SENTRY_DSN
        value: ""
      - key: NEXT_PUBLIC_VERCEL_ENV
        value: production

  # Backend API
  - type: web
    name: connect-backend
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    envVars:
      - key: ENV_MODE
        value: production
      - key: SUPABASE_URL
        value: https://irfvjdismpsxwihifsel.supabase.co
      - key: SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MjIxNDAsImV4cCI6MjA2NTM5ODE0MH0.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI
      - key: SUPABASE_SERVICE_ROLE_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgyMjE0MCwiZXhwIjoyMDY1Mzk4MTQwfQ.Z1k2HbdP-Z1m5mvqRw7ynWrkAPKUTJ11_8bzdDHxAM4
      - key: REDIS_HOST
        value: master-robin-17412.upstash.io
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_PASSWORD
        value: ""
      - key: REDIS_SSL
        value: "true"
      - key: REDIS_URL
        value: ""
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672
      - key: ANTHROPIC_API_KEY
        value: ""
      - key: OPENAI_API_KEY
        value: ""
      - key: MODEL_TO_USE
        value: ""
      - key: AWS_ACCESS_KEY_ID
        value: ""
      - key: AWS_SECRET_ACCESS_KEY
        value: ""
      - key: AWS_REGION_NAME
        value: ""
      - key: GROQ_API_KEY
        value: ""
      - key: OPENROUTER_API_KEY
        value: 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
      - key: RAPID_API_KEY
        value: 250c63d6e4msh224510ad4df47c0p1758b2jsn4767ad2cac62
      - key: TAVILY_API_KEY
        value: 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
      - key: FIRECRAWL_API_KEY
        value: fc-60fe43022991488e9f9d030a4d075b8b
      - key: FIRECRAWL_URL
        value: https://api.firecrawl.dev
      - key: DAYTONA_API_KEY
        value: ""
      - key: DAYTONA_SERVER_URL
        value: https://app.daytona.io/api
      - key: DAYTONA_TARGET
        value: us
      - key: LANGFUSE_PUBLIC_KEY
        value: ""
      - key: LANGFUSE_SECRET_KEY
        value: ""
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com
      - key: SMITHERY_API_KEY
        value: ""
      # Stripe Configuration
      - key: STRIPE_SECRET_KEY
        value: ""
      - key: STRIPE_WEBHOOK_SECRET
        value: ""
      # Email Configuration (Mailtrap)
      - key: MAILTRAP_API_TOKEN
        value: ""
      - key: MAILTRAP_SENDER_EMAIL
        value: <EMAIL>
      - key: MAILTRAP_SENDER_NAME
        value: Orchestra Connect
      # Stripe Configuration
      - key: STRIPE_SECRET_KEY
        value: ""
      - key: STRIPE_WEBHOOK_SECRET
        value: ""
      - key: STRIPE_PRODUCT_ID
        value: ""
      # Email Configuration (Mailtrap)
      - key: MAILTRAP_API_TOKEN
        value: ""
      - key: MAILTRAP_SENDER_EMAIL
        value: <EMAIL>
      - key: MAILTRAP_SENDER_NAME
        value: Orchestra Connect

  # Worker
  - type: worker
    name: connect-worker
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    dockerCommand: python -m dramatiq run_agent_background --processes 1 --threads 2
    envVars:
      - key: DRAMATIQ_THREADS
        value: "2"
      - key: DRAMATIQ_PROCESSES
        value: "1"
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      - key: PYTHONUNBUFFERED
        value: "1"
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com
      - key: LANGFUSE_SECRET_KEY
        value: ""
      - key: LANGFUSE_PUBLIC_KEY
        value: ""
      - key: AWS_REGION_NAME
        value: ""
      - key: AWS_SECRET_ACCESS_KEY
        value: ""
      - key: AWS_ACCESS_KEY_ID
        value: ""
      - key: RAPID_API_KEY
        value: placeholder
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: REDIS_SSL
        value: "true"
      - key: REDIS_PASSWORD
        value: ""
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_HOST
        value: master-robin-17412.upstash.io
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672
      - key: REDIS_URL
        value: ""
      - key: DAYTONA_TARGET
        value: us

  # RabbitMQ
  - type: pserv
    name: connect-rabbitmq
    runtime: docker
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./rabbitmq.Dockerfile
    envVars:
      - key: RABBITMQ_URL
        value: amqp://guest:guest@localhost:5672
    openPorts:
      - port: 4369
        protocol: tcp
      - port: 5672
        protocol: tcp
      - port: 15692
        protocol: tcp
      - port: 25672
        protocol: tcp 